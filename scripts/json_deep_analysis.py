#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度JSON分析工具
专门用于分析JSON文件中的格式问题和结构异常
"""

import json
import sys
import re
from collections import defaultdict, Counter
from typing import Dict, List, Any, Tuple


class JSONDeepAnalyzer:
    """JSON深度分析器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.content = ""
        self.total_chars = 0
        self.json_objects = []
        self.parse_errors = []
        self.persona_id_analysis = {}
        self.structure_analysis = {}
        
    def analyze(self):
        """执行深度分析"""
        print(f"🔍 开始深度分析文件: {self.file_path}")
        print("=" * 80)
        
        # 1. 读取文件
        self._load_file()
        
        # 2. 基本统计
        self._basic_statistics()
        
        # 3. JSON对象解析
        self._parse_json_objects()
        
        # 4. persona_id分析
        self._analyze_persona_ids()
        
        # 5. 结构分析
        self._analyze_structure()
        
        # 6. 生成报告
        self._generate_report()
    
    def _load_file(self):
        """加载文件内容"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.content = f.read()
            self.total_chars = len(self.content)
            print(f"✅ 文件加载成功，总字符数: {self.total_chars:,}")
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            sys.exit(1)
    
    def _basic_statistics(self):
        """基本统计信息"""
        print(f"\n📊 基本统计:")
        print(f"  文件大小: {self.total_chars:,} 字符")
        print(f"  行数: {self.content.count(chr(10)) + 1:,}")
        print(f"  '{{' 字符数: {self.content.count('{'):,}")
        print(f"  '}}' 字符数: {self.content.count('}'):,}")
        print(f"  'persona_id' 出现次数: {self.content.count('persona_id'):,}")
        
        # 检查括号平衡
        open_braces = self.content.count('{')
        close_braces = self.content.count('}')
        if open_braces == close_braces:
            print(f"  ✅ 括号平衡: {open_braces} 对")
        else:
            print(f"  ❌ 括号不平衡: {open_braces} 个 '{{', {close_braces} 个 '}}'")
    
    def _parse_json_objects(self):
        """解析JSON对象"""
        print(f"\n🔧 解析JSON对象:")
        
        decoder = json.JSONDecoder()
        idx = 0
        object_count = 0
        
        while idx < len(self.content):
            # 跳过空白字符
            while idx < len(self.content) and self.content[idx].isspace():
                idx += 1
            
            if idx >= len(self.content):
                break
            
            # 如果不是以 '{' 开始，寻找下一个 '{'
            if self.content[idx] != '{':
                next_brace = self.content.find('{', idx)
                if next_brace == -1:
                    break
                idx = next_brace
            
            try:
                # 尝试解析JSON对象
                obj, end_idx = decoder.raw_decode(self.content[idx:])
                object_count += 1
                
                # 存储成功解析的对象信息
                self.json_objects.append({
                    'index': object_count,
                    'start_pos': idx,
                    'end_pos': idx + end_idx,
                    'object': obj,
                    'size': end_idx
                })
                
                idx += end_idx
                
                # 进度显示
                if object_count % 1000 == 0:
                    print(f"  已解析 {object_count} 个对象...")
                    
            except json.JSONDecodeError as e:
                # 记录解析错误
                error_info = {
                    'position': idx,
                    'error': str(e),
                    'line': getattr(e, 'lineno', None),
                    'column': getattr(e, 'colno', None),
                    'context': self.content[max(0, idx-100):idx+200]
                }
                self.parse_errors.append(error_info)
                
                print(f"  ❌ 解析错误在位置 {idx}: {str(e)}")
                
                # 寻找下一个可能的JSON开始位置
                next_brace = self.content.find('{', idx + 1)
                if next_brace == -1:
                    break
                idx = next_brace
        
        print(f"  ✅ 成功解析 {len(self.json_objects)} 个JSON对象")
        print(f"  ❌ 遇到 {len(self.parse_errors)} 个解析错误")
    
    def _analyze_persona_ids(self):
        """分析persona_id"""
        print(f"\n🆔 persona_id分析:")
        
        persona_ids = []
        duplicate_ids = defaultdict(list)
        
        for obj_info in self.json_objects:
            obj = obj_info['object']
            if isinstance(obj, dict) and 'persona_id' in obj:
                persona_id = obj['persona_id']
                persona_ids.append(persona_id)
                duplicate_ids[persona_id].append(obj_info['index'])
        
        # 统计
        total_ids = len(persona_ids)
        unique_ids = len(set(persona_ids))
        duplicates = {pid: positions for pid, positions in duplicate_ids.items() if len(positions) > 1}
        
        print(f"  总persona_id数: {total_ids}")
        print(f"  唯一persona_id数: {unique_ids}")
        print(f"  重复persona_id数: {len(duplicates)}")
        
        if duplicates:
            print(f"  ❌ 发现重复的persona_id:")
            for i, (pid, positions) in enumerate(list(duplicates.items())[:5]):
                print(f"    {i+1}. {pid} 出现在对象: {positions}")
            if len(duplicates) > 5:
                print(f"    ... 还有 {len(duplicates) - 5} 个重复的persona_id")
        else:
            print(f"  ✅ 未发现重复的persona_id")
        
        self.persona_id_analysis = {
            'total': total_ids,
            'unique': unique_ids,
            'duplicates': duplicates
        }
    
    def _analyze_structure(self):
        """分析JSON结构"""
        print(f"\n🏗️ 结构分析:")
        
        field_counts = defaultdict(int)
        field_types = defaultdict(Counter)
        missing_fields = defaultdict(list)
        
        required_fields = [
            "persona_id", "StrengthsAndResources", "SocialSupportSystem", 
            "FormativeExperiences", "InterestsAndValues"
        ]
        
        for obj_info in self.json_objects:
            obj = obj_info['object']
            if not isinstance(obj, dict):
                continue
            
            # 统计字段
            for field in obj.keys():
                field_counts[field] += 1
                field_types[field][type(obj[field]).__name__] += 1
            
            # 检查缺失的必需字段
            for field in required_fields:
                if field not in obj:
                    missing_fields[field].append(obj_info['index'])
        
        print(f"  字段统计 (前10个最常见字段):")
        for field, count in sorted(field_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"    {field}: {count} 次")
        
        print(f"  必需字段检查:")
        for field in required_fields:
            count = field_counts.get(field, 0)
            expected = len(self.json_objects)
            if count == expected:
                print(f"    ✅ {field}: {count}/{expected}")
            else:
                missing_count = len(missing_fields.get(field, []))
                print(f"    ❌ {field}: {count}/{expected} (缺失 {missing_count})")
        
        self.structure_analysis = {
            'field_counts': dict(field_counts),
            'field_types': {k: dict(v) for k, v in field_types.items()},
            'missing_fields': dict(missing_fields)
        }
    
    def _generate_report(self):
        """生成详细报告"""
        print(f"\n📋 详细报告:")
        print("=" * 80)
        
        # 总结
        print(f"文件: {self.file_path}")
        print(f"总字符数: {self.total_chars:,}")
        print(f"成功解析的JSON对象: {len(self.json_objects)}")
        print(f"JSON解析错误: {len(self.parse_errors)}")
        print(f"persona_id总数: {self.persona_id_analysis['total']}")
        print(f"唯一persona_id数: {self.persona_id_analysis['unique']}")
        print(f"重复persona_id数: {len(self.persona_id_analysis['duplicates'])}")
        
        # 保存详细错误报告
        if self.parse_errors:
            error_file = self.file_path.replace('.jsonl', '_parse_errors.txt').replace('.json', '_parse_errors.txt')
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write(f"JSON解析错误详细报告 - {self.file_path}\n")
                f.write("=" * 80 + "\n\n")
                
                for i, error in enumerate(self.parse_errors, 1):
                    f.write(f"错误 {i}:\n")
                    f.write(f"  位置: {error['position']}\n")
                    f.write(f"  错误: {error['error']}\n")
                    if error.get('line'):
                        f.write(f"  行号: {error['line']}\n")
                    if error.get('column'):
                        f.write(f"  列号: {error['column']}\n")
                    f.write(f"  上下文:\n{error['context']}\n")
                    f.write("-" * 40 + "\n\n")
            
            print(f"📄 详细错误报告已保存到: {error_file}")
        
        # 保存重复persona_id报告
        duplicates = self.persona_id_analysis['duplicates']
        if duplicates:
            duplicate_file = self.file_path.replace('.jsonl', '_duplicate_ids.txt').replace('.json', '_duplicate_ids.txt')
            with open(duplicate_file, 'w', encoding='utf-8') as f:
                f.write(f"重复persona_id报告 - {self.file_path}\n")
                f.write("=" * 80 + "\n\n")
                f.write(f"总计: {len(duplicates)} 个重复的persona_id\n\n")
                
                for persona_id, positions in duplicates.items():
                    f.write(f"{persona_id}: 出现在对象 {positions}\n")
            
            print(f"📄 重复persona_id报告已保存到: {duplicate_file}")


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python json_deep_analysis.py <json_file_path>")
        print("示例: python json_deep_analysis.py expand_0726_4000.jsonl")
        sys.exit(1)
    
    file_path = sys.argv[1]
    analyzer = JSONDeepAnalyzer(file_path)
    analyzer.analyze()


if __name__ == "__main__":
    main()
