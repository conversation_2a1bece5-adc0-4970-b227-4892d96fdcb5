#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSONL文件格式检测脚本
检测指定字段的数量一致性和格式合理性
"""

import json
import sys
from collections import defaultdict
from typing import Dict, Any


class JSONLValidator:
    """JSONL文件验证器"""

    def __init__(self, file_path: str, base_file_path: str = None):
        self.file_path = file_path
        self.base_file_path = base_file_path
        self.required_fields = [
            "persona_id",
            "StrengthsAndResources",
            "SocialSupportSystem",
            "FormativeExperiences",
            "InterestsAndValues"
        ]
        self.errors = []
        self.field_counts = defaultdict(int)
        self.total_records = 0
        self.records_with_errors = []  # 存储有错误的记录信息
        self.persona_ids = []  # 存储所有persona_id
        self.base_persona_ids = {}  # 存储基础文件的persona_id映射
        self.missing_persona_suggestions = []  # 存储缺少persona_id的建议
        self.problematic_persona_ids = set()  # 存储所有有问题的persona_id
        self.duplicate_persona_ids = {}  # 存储重复的persona_id及其出现位置
        self.json_parse_errors = []  # 存储JSON解析错误的详细信息
        self.parsed_objects_count = 0  # 实际解析出的JSON对象数量

    def validate_file(self) -> Dict[str, Any]:
        """验证整个文件"""
        print(f"开始检测文件: {self.file_path}")
        if self.base_file_path:
            print(f"基础文件: {self.base_file_path}")
        print("=" * 60)

        # 如果提供了基础文件，先加载它
        if self.base_file_path:
            self._load_base_file()

        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            # 尝试解析为单个JSON对象
            try:
                data = json.loads(content)
                if isinstance(data, dict):
                    # 单个JSON对象 - 这是我们的情况
                    print("检测到单个大型JSON对象格式")
                    self._validate_single_record(data, 1)
                    self.total_records = 1
                elif isinstance(data, list):
                    # JSON数组
                    print(f"检测到JSON数组格式，包含{len(data)}个记录")
                    for i, record in enumerate(data, 1):
                        self._validate_single_record(record, i)
                    self.total_records = len(data)
                else:
                    self.errors.append("文件内容不是有效的JSON对象或数组")

            except json.JSONDecodeError as e:
                print(f"JSON解析失败，尝试其他格式解析: {str(e)}")

                # 记录JSON解析错误
                self.json_parse_errors.append({
                    "error_type": "primary_parse_error",
                    "error_message": str(e),
                    "position": getattr(e, 'pos', None),
                    "line": getattr(e, 'lineno', None),
                    "column": getattr(e, 'colno', None)
                })

                # 尝试解析连续的JSON对象（没有逗号分隔的多个JSON对象）
                if "Extra data" in str(e):
                    print("检测到连续JSON对象格式，尝试分割解析...")
                    self._parse_consecutive_json_objects(content)
                else:
                    # 尝试按JSONL格式解析（每行一个JSON）
                    print("尝试按JSONL格式解析...")
                    lines = content.split('\n')
                    valid_lines = 0
                    for line_num, line in enumerate(lines, 1):
                        line = line.strip()
                        if not line:
                            continue
                        try:
                            record = json.loads(line)
                            self._validate_single_record(record, line_num)
                            valid_lines += 1
                        except json.JSONDecodeError as line_error:
                            # 记录JSON解析错误详情
                            self._record_json_parse_error(line_error, line_num, line)

                    self.total_records = valid_lines
                    if valid_lines == 0:
                        self.errors.append("未找到任何有效的JSON记录")

        except FileNotFoundError:
            self.errors.append(f"文件不存在: {self.file_path}")
        except Exception as e:
            self.errors.append(f"读取文件时发生错误: {str(e)}")

        # 查找缺失的persona_id（在基础文件中存在但在扩展文件中缺失）
        if self.base_persona_ids:
            self._find_missing_persona_ids()

        # 检查persona_id重复
        self._check_duplicate_persona_ids()

        return self._generate_report()

    def _load_base_file(self):
        """加载基础文件，建立persona_id映射"""
        try:
            print(f"正在加载基础文件: {self.base_file_path}")
            with open(self.base_file_path, 'r', encoding='utf-8') as f:
                base_data = json.load(f)

            if isinstance(base_data, list):
                for i, record in enumerate(base_data):
                    if isinstance(record, dict) and 'persona_id' in record:
                        persona_id = record['persona_id']
                        self.base_persona_ids[persona_id] = {
                            'index': i,
                            'record': record
                        }
                print(f"成功加载基础文件，包含 {len(self.base_persona_ids)} 个persona_id")
            else:
                print("警告：基础文件不是数组格式")

        except FileNotFoundError:
            print(f"警告：基础文件不存在: {self.base_file_path}")
        except json.JSONDecodeError as e:
            print(f"警告：基础文件JSON格式错误: {str(e)}")
        except Exception as e:
            print(f"警告：加载基础文件时发生错误: {str(e)}")

    def _parse_consecutive_json_objects(self, content: str):
        """解析连续的JSON对象（没有逗号分隔）"""
        decoder = json.JSONDecoder()
        idx = 0
        record_num = 0
        total_parsed = 0

        print(f"开始解析文件，总长度: {len(content)} 字符")

        while idx < len(content):
            content_from_idx = content[idx:].lstrip()
            if not content_from_idx:
                break

            try:
                obj, end_idx = decoder.raw_decode(content_from_idx)
                record_num += 1
                total_parsed += 1
                self.parsed_objects_count += 1
                self._validate_single_record(obj, record_num)
                idx += len(content[idx:]) - len(content_from_idx) + end_idx

                # 每1000个对象打印一次进度
                if total_parsed % 1000 == 0:
                    print(f"已解析 {total_parsed} 个JSON对象...")

            except json.JSONDecodeError as e:
                # 记录JSON解析错误详情
                error_position = idx + len(content[idx:]) - len(content_from_idx)

                # 获取错误周围的上下文
                context_start = max(0, error_position - 200)
                context_end = min(len(content), error_position + 200)
                error_context = content[context_start:context_end]

                self._record_json_parse_error(e, record_num + 1, error_context, error_position)

                print(f"JSON解析错误在位置 {error_position}: {str(e)}")
                print(f"错误上下文: {error_context[:100]}...")

                # 尝试找到下一个可能的JSON开始位置
                next_brace = content.find('{', idx + 1)
                if next_brace == -1:
                    print("未找到更多JSON对象开始标记，停止解析")
                    break

                # 跳过错误部分，继续解析
                skipped_chars = next_brace - idx
                print(f"跳过 {skipped_chars} 个字符，继续从位置 {next_brace} 解析")
                idx = next_brace

        self.total_records = record_num
        print(f"解析完成：成功解析 {record_num} 个有效JSON对象，总共尝试解析 {total_parsed} 个对象")
        if len(self.json_parse_errors) > 0:
            print(f"遇到 {len(self.json_parse_errors)} 个JSON解析错误")

    def _find_missing_persona_ids(self):
        """查找在基础文件中存在但在扩展文件中缺失的persona_id"""
        if not self.base_persona_ids:
            return

        base_ids = set(self.base_persona_ids.keys())
        expand_ids = set([p["id"] for p in self.persona_ids])

        missing_ids = base_ids - expand_ids

        print(f"基础文件中有 {len(base_ids)} 个persona_id")
        print(f"扩展文件中有 {len(expand_ids)} 个persona_id")
        print(f"缺失的persona_id数量: {len(missing_ids)}")

        # 将缺失的persona_id也加入到需要重新生成的列表中
        for missing_id in missing_ids:
            self.problematic_persona_ids.add(missing_id)

            # 添加到错误记录中以便在报告中显示
            self.records_with_errors.append({
                "record_num": "缺失",
                "persona_id": missing_id,
                "missing_fields": ["完整记录"],
                "format_errors": [],
                "sample_content": "在基础文件中存在，但在扩展文件中完全缺失",
                "suggested_persona_id": None
            })

    def _validate_single_record(self, record: Dict[str, Any], record_num: int):
        """验证单条记录"""
        if not isinstance(record, dict):
            self.errors.append(f"记录{record_num}: 不是有效的JSON对象")
            return

        # 检查是否是JSON片段而不是完整记录
        if self._is_json_fragment(record):
            # 这是JSON片段，不是完整记录，跳过验证
            return

        # 获取persona_id用于错误定位
        persona_id = record.get("persona_id", f"未知ID(记录{record_num})")
        if "persona_id" in record:
            self.persona_ids.append({
                "id": persona_id,
                "record_num": record_num,
                "position": len(self.persona_ids) + 1
            })

        record_errors = []
        missing_fields = []

        # 检查必需字段是否存在
        for field in self.required_fields:
            if field in record:
                self.field_counts[field] += 1
                field_errors = self._validate_field_format(record, field, record_num, persona_id)
                record_errors.extend(field_errors)
            else:
                missing_fields.append(field)
                self.errors.append(f"记录{record_num} (persona_id: {persona_id}): 缺少必需字段 '{field}'")

        # 如果有错误，记录详细信息
        if record_errors or missing_fields:
            error_info = {
                "record_num": record_num,
                "persona_id": persona_id,
                "missing_fields": missing_fields,
                "format_errors": record_errors,
                "sample_content": self._get_record_sample(record),
                "suggested_persona_id": None
            }

            # 记录有问题的persona_id
            if "persona_id" not in missing_fields and persona_id != f"未知ID(记录{record_num})":
                # 如果有persona_id且不是未知ID，记录它
                self.problematic_persona_ids.add(persona_id)

            # 如果缺少persona_id，尝试从基础文件中查找匹配的记录
            if "persona_id" in missing_fields and self.base_persona_ids:
                suggested_id = self._find_matching_persona_id(record)
                if suggested_id:
                    error_info["suggested_persona_id"] = suggested_id
                    self.missing_persona_suggestions.append({
                        "record_num": record_num,
                        "suggested_persona_id": suggested_id,
                        "match_info": self._get_match_info(record, suggested_id)
                    })
                    # 也记录建议的persona_id作为需要重新生成的
                    self.problematic_persona_ids.add(suggested_id)

            self.records_with_errors.append(error_info)

    def _record_json_parse_error(self, error: Exception, record_num: int, content_preview: str, position: int = None):
        """记录JSON解析错误的详细信息"""
        error_info = {
            "record_num": record_num,
            "error_message": str(error),
            "error_type": type(error).__name__,
            "position": position or getattr(error, 'pos', None),
            "line": getattr(error, 'lineno', None),
            "column": getattr(error, 'colno', None),
            "content_preview": content_preview[:200] + "..." if len(content_preview) > 200 else content_preview
        }
        self.json_parse_errors.append(error_info)

        # 只记录前10个JSON解析错误到主错误列表，避免输出过多
        if len([e for e in self.errors if "JSON格式错误" in e]) < 10:
            self.errors.append(f"记录{record_num}附近JSON格式错误: {str(error)}")

    def _check_duplicate_persona_ids(self):
        """检查persona_id重复"""
        persona_id_counts = {}

        # 统计每个persona_id的出现次数和位置
        for persona_info in self.persona_ids:
            persona_id = persona_info["id"]
            if persona_id not in persona_id_counts:
                persona_id_counts[persona_id] = []
            persona_id_counts[persona_id].append(persona_info)

        # 找出重复的persona_id
        for persona_id, occurrences in persona_id_counts.items():
            if len(occurrences) > 1:
                self.duplicate_persona_ids[persona_id] = occurrences
                # 将重复的persona_id标记为有问题的
                self.problematic_persona_ids.add(persona_id)

                # 添加到错误列表
                positions = [f"记录{occ['record_num']}" for occ in occurrences]
                error_msg = f"persona_id重复: {persona_id} 出现在 {', '.join(positions)}"
                self.errors.append(error_msg)

    def _get_record_sample(self, record: Dict[str, Any]) -> str:
        """获取记录的样本内容用于错误定位"""
        sample_fields = ["Gender", "Age", "Occupation", "Topic", "Subtopic"]
        sample_parts = []

        for field in sample_fields:
            if field in record:
                value = str(record[field])
                if len(value) > 50:
                    value = value[:50] + "..."
                sample_parts.append(f"{field}: {value}")

        return " | ".join(sample_parts) if sample_parts else "无可识别内容"

    def _is_json_fragment(self, record: Dict[str, Any]) -> bool:
        """检查是否是JSON片段而不是完整的persona记录"""
        keys = set(record.keys())

        # 如果只包含FormativeExperiences的子字段，认为是片段
        formative_exp_keys = {"事件", "影响"}
        if keys.issubset(formative_exp_keys) and len(keys) > 0:
            return True

        # 如果只包含InterestsAndValues的子字段，认为是片段
        interests_values_keys = {"Interests", "Values"}
        if keys.issubset(interests_values_keys) and len(keys) > 0:
            return True

        # 如果只包含其他明显的子字段组合，认为是片段
        # 可以根据需要添加更多规则

        return False

    def _find_matching_persona_id(self, record: Dict[str, Any]) -> str:
        """从基础文件中查找匹配的persona_id"""
        if not self.base_persona_ids:
            return None

        # 匹配字段优先级：Gender > Age > Occupation > Topic > Subtopic
        match_fields = ["Gender", "Age", "Occupation", "Topic", "Subtopic"]
        best_match = None
        best_score = 0

        for persona_id, base_info in self.base_persona_ids.items():
            base_record = base_info['record']
            score = 0

            # 计算匹配分数
            for field in match_fields:
                if field in record and field in base_record:
                    if record[field] == base_record[field]:
                        # 不同字段有不同权重
                        if field == "Gender":
                            score += 3
                        elif field == "Age":
                            score += 2
                        elif field == "Occupation":
                            score += 2
                        elif field == "Topic":
                            score += 1
                        elif field == "Subtopic":
                            score += 1

            # 如果找到更好的匹配
            if score > best_score and score >= 5:  # 至少需要5分才认为是匹配
                best_score = score
                best_match = persona_id

        return best_match

    def _get_match_info(self, record: Dict[str, Any], persona_id: str) -> str:
        """获取匹配信息描述"""
        if not persona_id or persona_id not in self.base_persona_ids:
            return "无匹配信息"

        base_record = self.base_persona_ids[persona_id]['record']
        match_fields = []

        for field in ["Gender", "Age", "Occupation", "Topic", "Subtopic"]:
            if field in record and field in base_record and record[field] == base_record[field]:
                match_fields.append(f"{field}: {record[field]}")

        return " | ".join(match_fields) if match_fields else "无匹配字段"

    def _validate_field_format(self, record: Dict[str, Any], field: str, record_num: int, persona_id: str):
        """验证字段格式，返回错误列表"""
        value = record[field]
        errors = []

        if field == "persona_id":
            errors.extend(self._validate_persona_id(value, record_num, persona_id))
        elif field == "StrengthsAndResources":
            errors.extend(self._validate_strengths_and_resources(value, record_num, persona_id))
        elif field == "SocialSupportSystem":
            errors.extend(self._validate_social_support_system(value, record_num, persona_id))
        elif field == "FormativeExperiences":
            errors.extend(self._validate_formative_experiences(value, record_num, persona_id))
        elif field == "InterestsAndValues":
            errors.extend(self._validate_interests_and_values(value, record_num, persona_id))

        return errors

    def _validate_persona_id(self, value: Any, record_num: int, persona_id: str):
        """验证persona_id格式"""
        errors = []

        if not isinstance(value, str):
            error_msg = f"记录{record_num} (persona_id: {persona_id}): persona_id应为字符串类型，实际为{type(value).__name__}"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        if not value.strip():
            error_msg = f"记录{record_num} (persona_id: {persona_id}): persona_id不能为空"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        # 检查是否为UUID格式（可选）
        import re
        uuid_pattern = r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
        if not re.match(uuid_pattern, value.lower()):
            error_msg = f"记录{record_num} (persona_id: {persona_id}): persona_id格式不符合UUID标准: {value}"
            self.errors.append(error_msg)
            errors.append(error_msg)

        return errors

    def _validate_strengths_and_resources(self, value: Any, record_num: int, persona_id: str):
        """验证StrengthsAndResources格式"""
        errors = []

        if not isinstance(value, list):
            error_msg = f"记录{record_num} (persona_id: {persona_id}): StrengthsAndResources应为数组类型，实际为{type(value).__name__}"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        if len(value) == 0:
            error_msg = f"记录{record_num} (persona_id: {persona_id}): StrengthsAndResources不能为空数组"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        for i, item in enumerate(value):
            if not isinstance(item, str):
                error_msg = f"记录{record_num} (persona_id: {persona_id}): StrengthsAndResources[{i}]应为字符串类型"
                self.errors.append(error_msg)
                errors.append(error_msg)
            elif not item.strip():
                error_msg = f"记录{record_num} (persona_id: {persona_id}): StrengthsAndResources[{i}]不能为空字符串"
                self.errors.append(error_msg)
                errors.append(error_msg)

        return errors

    def _validate_social_support_system(self, value: Any, record_num: int, persona_id: str):
        """验证SocialSupportSystem格式"""
        errors = []

        if not isinstance(value, dict):
            error_msg = f"记录{record_num} (persona_id: {persona_id}): SocialSupportSystem应为对象类型，实际为{type(value).__name__}"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        if len(value) == 0:
            error_msg = f"记录{record_num} (persona_id: {persona_id}): SocialSupportSystem不能为空对象"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        for key, val in value.items():
            if not isinstance(key, str) or not key.strip():
                error_msg = f"记录{record_num} (persona_id: {persona_id}): SocialSupportSystem的键应为非空字符串"
                self.errors.append(error_msg)
                errors.append(error_msg)
            if not isinstance(val, str) or not val.strip():
                error_msg = f"记录{record_num} (persona_id: {persona_id}): SocialSupportSystem['{key}']的值应为非空字符串"
                self.errors.append(error_msg)
                errors.append(error_msg)

        return errors

    def _validate_formative_experiences(self, value: Any, record_num: int, persona_id: str):
        """验证FormativeExperiences格式"""
        errors = []

        if not isinstance(value, list):
            error_msg = f"记录{record_num} (persona_id: {persona_id}): FormativeExperiences应为数组类型，实际为{type(value).__name__}"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        if len(value) == 0:
            error_msg = f"记录{record_num} (persona_id: {persona_id}): FormativeExperiences不能为空数组"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        for i, item in enumerate(value):
            if not isinstance(item, dict):
                error_msg = f"记录{record_num} (persona_id: {persona_id}): FormativeExperiences[{i}]应为对象类型"
                self.errors.append(error_msg)
                errors.append(error_msg)
                continue

            # 检查是否包含"事件"字段
            if "事件" not in item:
                error_msg = f"记录{record_num} (persona_id: {persona_id}): FormativeExperiences[{i}]缺少'事件'字段"
                self.errors.append(error_msg)
                errors.append(error_msg)
            elif not isinstance(item["事件"], str) or not item["事件"].strip():
                error_msg = f"记录{record_num} (persona_id: {persona_id}): FormativeExperiences[{i}]['事件']应为非空字符串"
                self.errors.append(error_msg)
                errors.append(error_msg)

        return errors

    def _validate_interests_and_values(self, value: Any, record_num: int, persona_id: str):
        """验证InterestsAndValues格式"""
        errors = []

        if not isinstance(value, dict):
            error_msg = f"记录{record_num} (persona_id: {persona_id}): InterestsAndValues应为对象类型，实际为{type(value).__name__}"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        if len(value) == 0:
            error_msg = f"记录{record_num} (persona_id: {persona_id}): InterestsAndValues不能为空对象"
            self.errors.append(error_msg)
            errors.append(error_msg)
            return errors

        # 检查是否包含Interests字段
        if "Interests" not in value:
            error_msg = f"记录{record_num} (persona_id: {persona_id}): InterestsAndValues缺少'Interests'字段"
            self.errors.append(error_msg)
            errors.append(error_msg)
        elif not isinstance(value["Interests"], list):
            error_msg = f"记录{record_num} (persona_id: {persona_id}): InterestsAndValues['Interests']应为数组类型"
            self.errors.append(error_msg)
            errors.append(error_msg)
        elif len(value["Interests"]) == 0:
            error_msg = f"记录{record_num} (persona_id: {persona_id}): InterestsAndValues['Interests']不能为空数组"
            self.errors.append(error_msg)
            errors.append(error_msg)

        return errors

    def _generate_report(self) -> Dict[str, Any]:
        """生成检测报告"""
        report = {
            "file_path": self.file_path,
            "base_file_path": self.base_file_path,
            "total_records": self.total_records,
            "parsed_objects_count": self.parsed_objects_count,
            "field_counts": dict(self.field_counts),
            "errors": self.errors,
            "records_with_errors": self.records_with_errors,
            "persona_ids": self.persona_ids,
            "duplicate_persona_ids": self.duplicate_persona_ids,
            "json_parse_errors": self.json_parse_errors,
            "missing_persona_suggestions": self.missing_persona_suggestions,
            "base_persona_count": len(self.base_persona_ids) if self.base_persona_ids else 0,
            "problematic_persona_ids": list(self.problematic_persona_ids),
            "summary": {}
        }

        # 生成摘要
        if self.total_records > 0:
            expected_count = self.total_records
            inconsistent_fields = []

            for field in self.required_fields:
                actual_count = self.field_counts[field]
                if actual_count != expected_count:
                    inconsistent_fields.append({
                        "field": field,
                        "expected": expected_count,
                        "actual": actual_count,
                        "difference": actual_count - expected_count
                    })

            report["summary"] = {
                "total_errors": len(self.errors),
                "inconsistent_fields": inconsistent_fields,
                "is_valid": len(self.errors) == 0 and len(inconsistent_fields) == 0
            }

        return report


def print_report(report: Dict[str, Any]):
    """打印检测报告"""
    print(f"\n📊 检测报告")
    print("=" * 60)
    print(f"文件路径: {report['file_path']}")
    if report.get('base_file_path'):
        print(f"基础文件: {report['base_file_path']} (包含 {report.get('base_persona_count', 0)} 个persona_id)")
    print(f"总记录数: {report['total_records']}")

    # 显示解析统计
    parsed_count = report.get('parsed_objects_count', 0)
    if parsed_count > 0 and parsed_count != report['total_records']:
        print(f"解析出的JSON对象数: {parsed_count}")

    # 显示persona_id统计
    persona_ids = report.get('persona_ids', [])
    unique_persona_count = len(set([p["id"] for p in persona_ids]))
    print(f"persona_id总数: {len(persona_ids)} (唯一: {unique_persona_count})")

    # 显示重复persona_id
    duplicate_ids = report.get('duplicate_persona_ids', {})
    if duplicate_ids:
        print(f"❌ 发现 {len(duplicate_ids)} 个重复的persona_id:")
        for i, (persona_id, occurrences) in enumerate(list(duplicate_ids.items())[:5], 1):
            positions = [f"记录{occ['record_num']}" for occ in occurrences]
            print(f"  {i}. {persona_id} 出现在: {', '.join(positions)}")
        if len(duplicate_ids) > 5:
            print(f"  ... 还有{len(duplicate_ids) - 5}个重复的persona_id")
    else:
        print(f"✅ 未发现重复的persona_id")

    # 显示JSON解析错误
    json_errors = report.get('json_parse_errors', [])
    if json_errors:
        print(f"❌ JSON解析错误: {len(json_errors)} 个")
        for i, error in enumerate(json_errors[:3], 1):
            record_info = error.get('record_num', '未知')
            print(f"  {i}. 记录{record_info}: {error['error_message']}")
            if error.get('position'):
                print(f"     位置: {error['position']}")
            if error.get('content_preview'):
                preview = error['content_preview'][:100] + "..." if len(error['content_preview']) > 100 else error['content_preview']
                print(f"     内容预览: {preview}")
        if len(json_errors) > 3:
            print(f"  ... 还有{len(json_errors) - 3}个JSON解析错误")
    else:
        print(f"✅ 未发现JSON解析错误")

    print(f"\n📈 字段统计:")
    for field, count in report['field_counts'].items():
        print(f"  {field}: {count}")

    # 字段数量一致性检查
    inconsistent = report['summary'].get('inconsistent_fields', [])
    if inconsistent:
        print(f"\n❌ 字段数量不一致问题:")
        for item in inconsistent:
            print(f"  {item['field']}: 期望{item['expected']}个，实际{item['actual']}个 (差异: {item['difference']:+d})")
    else:
        print(f"\n✅ 所有字段数量一致")

    # 详细错误记录信息
    error_records = report.get('records_with_errors', [])
    if error_records:
        print(f"\n❌ 有问题的记录详情 (共{len(error_records)}条):")

        # 显示前20条有问题的记录
        for i, record_info in enumerate(error_records[:20], 1):
            print(f"\n  {i}. 记录{record_info['record_num']} (persona_id: {record_info['persona_id']})")
            print(f"     内容样本: {record_info['sample_content']}")

            if record_info['missing_fields']:
                print(f"     缺少字段: {', '.join(record_info['missing_fields'])}")

                # 如果缺少persona_id且有建议，显示建议
                if 'persona_id' in record_info['missing_fields'] and record_info.get('suggested_persona_id'):
                    print(f"     💡 建议persona_id: {record_info['suggested_persona_id']}")

            if record_info['format_errors']:
                print(f"     格式错误: {len(record_info['format_errors'])}个")
                for error in record_info['format_errors'][:3]:  # 只显示前3个格式错误
                    print(f"       - {error}")
                if len(record_info['format_errors']) > 3:
                    print(f"       - ... 还有{len(record_info['format_errors']) - 3}个格式错误")

        if len(error_records) > 20:
            print(f"\n  ... 还有{len(error_records) - 20}条有问题的记录未显示")

    # 显示persona_id建议
    persona_suggestions = report.get('missing_persona_suggestions', [])
    if persona_suggestions:
        print(f"\n💡 缺少persona_id的建议 (共{len(persona_suggestions)}条):")
        for i, suggestion in enumerate(persona_suggestions, 1):
            print(f"  {i}. 记录{suggestion['record_num']}")
            print(f"     建议persona_id: {suggestion['suggested_persona_id']}")
            print(f"     匹配信息: {suggestion['match_info']}")
            if i >= 5:  # 只显示前5条建议
                remaining = len(persona_suggestions) - 5
                if remaining > 0:
                    print(f"  ... 还有{remaining}条建议未显示")
                break

        # 生成详细的错误记录列表文件
        if error_records:
            file_path = report['file_path']
            error_file = file_path.replace('.jsonl', '_errors.txt').replace('.json', '_errors.txt')
            with open(error_file, 'w', encoding='utf-8') as f:
                f.write(f"错误记录详细列表 - {file_path}\n")
                f.write("=" * 80 + "\n\n")

                for i, record_info in enumerate(error_records, 1):
                    f.write(f"{i}. 记录{record_info['record_num']} (persona_id: {record_info['persona_id']})\n")
                    f.write(f"   内容样本: {record_info['sample_content']}\n")

                    if record_info['missing_fields']:
                        f.write(f"   缺少字段: {', '.join(record_info['missing_fields'])}\n")

                        # 如果缺少persona_id且有建议，写入建议
                        if 'persona_id' in record_info['missing_fields'] and record_info.get('suggested_persona_id'):
                            f.write(f"   💡 建议persona_id: {record_info['suggested_persona_id']}\n")

                    if record_info['format_errors']:
                        f.write(f"   格式错误:\n")
                        for error in record_info['format_errors']:
                            f.write(f"     - {error}\n")
                    f.write("\n")

                # 添加persona_id建议汇总
                persona_suggestions = report.get('missing_persona_suggestions', [])
                if persona_suggestions:
                    f.write("\n" + "=" * 80 + "\n")
                    f.write("缺少persona_id的建议汇总\n")
                    f.write("=" * 80 + "\n\n")

                    for i, suggestion in enumerate(persona_suggestions, 1):
                        f.write(f"{i}. 记录{suggestion['record_num']}\n")
                        f.write(f"   建议persona_id: {suggestion['suggested_persona_id']}\n")
                        f.write(f"   匹配信息: {suggestion['match_info']}\n\n")

            print(f"\n📄 详细错误记录已保存到: {error_file}")

    # 格式错误总数
    errors = report['errors']
    if errors:
        print(f"\n❌ 总格式错误数: {len(errors)}")
    else:
        print(f"\n✅ 未发现格式错误")

    # 生成需要重新生成的persona_id列表文件
    problematic_ids = report.get('problematic_persona_ids', [])
    if problematic_ids:
        file_path = report['file_path']
        persona_ids_file = file_path.replace('.jsonl', '_problematic_persona_ids.txt').replace('.json', '_problematic_persona_ids.txt')

        with open(persona_ids_file, 'w', encoding='utf-8') as f:
            f.write(f"需要重新生成的persona_id列表 - {file_path}\n")
            f.write(f"基础文件: {report.get('base_file_path', '未指定')}\n")
            f.write(f"生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")
            f.write(f"总计: {len(problematic_ids)} 个persona_id需要重新生成\n\n")

            # 按类别分组
            format_error_ids = []
            missing_ids = []

            for record_info in report.get('records_with_errors', []):
                persona_id = record_info['persona_id']
                if record_info['record_num'] == "缺失":
                    missing_ids.append(persona_id)
                elif persona_id in problematic_ids:
                    format_error_ids.append(persona_id)

            if missing_ids:
                f.write(f"1. 完全缺失的记录 ({len(missing_ids)}个):\n")
                for pid in missing_ids:
                    f.write(f"{pid}\n")
                f.write("\n")

            if format_error_ids:
                f.write(f"2. 格式错误的记录 ({len(format_error_ids)}个):\n")
                for pid in format_error_ids:
                    f.write(f"{pid}\n")
                f.write("\n")

            # 所有persona_id的完整列表
            f.write("=" * 80 + "\n")
            f.write("完整列表 (用于批量处理):\n")
            f.write("=" * 80 + "\n")
            for pid in sorted(problematic_ids):
                f.write(f"{pid}\n")

        print(f"\n📄 需要重新生成的persona_id列表已保存到: {persona_ids_file}")
        print(f"    包含 {len(problematic_ids)} 个persona_id")

    # 总结
    print(f"\n📋 总结:")
    print(f"  总错误数: {report['summary'].get('total_errors', 0)}")
    print(f"  需要重新生成的persona_id数量: {len(problematic_ids)}")
    print(f"  验证结果: {'✅ 通过' if report['summary'].get('is_valid', False) else '❌ 未通过'}")


def main():
    """主函数"""
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("用法: python invalid_detect.py <jsonl_file_path> [base_file_path]")
        print("示例: python invalid_detect.py expand_0726_4000.jsonl")
        print("示例: python invalid_detect.py expand_0726_4000.jsonl generated_personas_0726.json")
        sys.exit(1)

    file_path = sys.argv[1]
    base_file_path = sys.argv[2] if len(sys.argv) == 3 else None

    # 如果没有提供基础文件，尝试自动推断
    if not base_file_path:
        # 尝试从文件名推断基础文件
        if "expand_0726_4000" in file_path:
            potential_base = file_path.replace("expand_0726_4000.jsonl", "generated_personas_0726.json")
            potential_base = potential_base.replace("expand_0726_4000.json", "generated_personas_0726.json")
            import os
            if os.path.exists(potential_base):
                base_file_path = potential_base
                print(f"自动检测到基础文件: {base_file_path}")

    validator = JSONLValidator(file_path, base_file_path)
    report = validator.validate_file()
    print_report(report)

    # 如果有错误，返回非零退出码
    if not report['summary'].get('is_valid', False):
        sys.exit(1)


if __name__ == "__main__":
    main()