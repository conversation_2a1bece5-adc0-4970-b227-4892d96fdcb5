import json
import os
import sys

def count_json_items(file_path):
    """统计JSON文件中的条目数量"""
    with open(file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
        
    if isinstance(data, dict):
        return len(data)
    elif isinstance(data, list):
        return len(data)
    else:
        return 1  # 单个值

def main():
    if len(sys.argv) != 2:
        print("使用方法: python cal.py <json文件路径>")
        print("示例: python cal.py data.json")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    count = count_json_items(file_path)
    
    if count is not None:
        print(f"JSON文件 '{file_path}' 中包含 {count} 个条目")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
