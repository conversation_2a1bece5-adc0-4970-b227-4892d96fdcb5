#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版JSONL转JSON转换器
使用标准JSON解析器而不是括号计数法
"""

import json
import sys
from pathlib import Path


def parse_consecutive_json_correct(content):
    """
    使用标准JSON解析器解析连续的JSON对象
    这是正确的方法，不会将嵌套对象误认为独立对象
    """
    json_objects = []
    decoder = json.JSONDecoder()
    idx = 0
    
    print(f"开始解析，总长度: {len(content)} 字符")
    
    while idx < len(content):
        # 跳过空白字符
        content_from_idx = content[idx:].lstrip()
        if not content_from_idx:
            break
        
        try:
            # 使用标准JSON解析器
            obj, end_idx = decoder.raw_decode(content_from_idx)
            json_objects.append(obj)
            idx += len(content[idx:]) - len(content_from_idx) + end_idx
            
            # 进度显示
            if len(json_objects) % 1000 == 0:
                print(f"已解析 {len(json_objects)} 个对象...")
                
        except json.JSONDecodeError as e:
            print(f"警告: 解析JSON对象时出错: {e}")
            error_pos = idx + len(content[idx:]) - len(content_from_idx)
            print(f"位置: {error_pos}")
            
            # 显示错误上下文
            context_start = max(0, error_pos - 100)
            context_end = min(len(content), error_pos + 200)
            context = content[context_start:context_end]
            print(f"内容预览: {context[:200]}...")
            
            # 尝试找到下一个JSON对象开始位置
            next_brace = content.find('{', idx + 1)
            if next_brace == -1:
                break
            idx = next_brace
    
    return json_objects


def convert_file(input_file, output_file=None):
    """
    转换单个文件
    """
    input_path = Path(input_file)
    
    if not input_path.exists():
        print(f"错误: 文件不存在 - {input_file}")
        return False
    
    # 生成输出文件名
    if output_file is None:
        output_file = input_path.stem + '_fixed.json'
    
    print(f"正在处理: {input_file}")
    
    try:
        # 读取文件
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用正确的方法解析JSON对象
        json_objects = parse_consecutive_json_correct(content)
        
        if not json_objects:
            print(f"错误: 在文件 {input_file} 中未找到有效的JSON对象")
            return False
        
        print(f"找到 {len(json_objects)} 个JSON对象")
        
        # 验证解析结果
        persona_ids = []
        for obj in json_objects:
            if isinstance(obj, dict) and 'persona_id' in obj:
                persona_ids.append(obj['persona_id'])
        
        unique_personas = len(set(persona_ids))
        print(f"包含 {len(persona_ids)} 个persona_id (唯一: {unique_personas})")
        
        if len(persona_ids) != unique_personas:
            print(f"警告: 发现 {len(persona_ids) - unique_personas} 个重复的persona_id")
        
        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_objects, f, ensure_ascii=False, indent=2)
        
        print(f"转换完成: {output_file}")
        return True
        
    except Exception as e:
        print(f"错误: 处理文件 {input_file} 时出现异常: {e}")
        return False


def main():
    """
    主函数 - 处理命令行参数
    """
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python jsonl2json_fixed.py <输入文件> [输出文件]")
        print("")
        print("示例:")
        print("  python jsonl2json_fixed.py expand_0726_4000.jsonl")
        print("  python jsonl2json_fixed.py expand_0726_4000.jsonl output.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    success = convert_file(input_file, output_file)
    
    if success:
        print("转换成功!")
    else:
        print("转换失败!")
        sys.exit(1)


if __name__ == '__main__':
    main()
