#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机删除JSON文件中包含特定中文词汇的字段内容
"""

import json
import random
import argparse
import sys
from typing import Dict, List

# 默认配置 - 当不提供命令行参数时使用这些值
JSON_FILE = "expand_0726_4000_1.json"
KEYWORD = "迷茫"
OUTPUT_FILE = "expand_0726_4000_1.json"  # 如果为空，将自动生成文件名

# 支持的字段列表
SUPPORTED_FIELDS = [
    "Emotional Experience Words"
]
    # "StrengthsAndResources",
    # "SocialSupportSystem",
    # "FormativeExperiences",
    # "InterestsAndValues"
    # "Personality",
    # "Emotional Experience Words",

def count_keyword_occurrences(data: List[Dict], keyword: str, target_fields: List[str] = None) -> int:
    """统计关键词在指定字段中的出现次数"""
    if target_fields is None:
        target_fields = SUPPORTED_FIELDS

    total_count = 0

    for item in data:
        # Personality - list of strings
        if "Personality" in target_fields and "Personality" in item:
            for personality in item["Personality"]:
                if keyword in personality:
                    total_count += 1

        # Emotional Experience Words - list of strings
        if "Emotional Experience Words" in target_fields and "Emotional Experience Words" in item:
            for emotion in item["Emotional Experience Words"]:
                if keyword in emotion:
                    total_count += 1

        # StrengthsAndResources - list of strings
        if "StrengthsAndResources" in target_fields and "StrengthsAndResources" in item:
            for strength in item["StrengthsAndResources"]:
                if keyword in strength:
                    total_count += 1

        # SocialSupportSystem - dict with key-value pairs
        if "SocialSupportSystem" in target_fields and "SocialSupportSystem" in item:
            for key, value in item["SocialSupportSystem"].items():
                if keyword in key or keyword in value:
                    total_count += 1

        # FormativeExperiences - list of dicts
        if "FormativeExperiences" in target_fields and "FormativeExperiences" in item:
            for experience in item["FormativeExperiences"]:
                # 检查整个experience dict中是否包含关键词
                experience_str = json.dumps(experience, ensure_ascii=False)
                if keyword in experience_str:
                    total_count += 1

        # InterestsAndValues - dict with lists as values
        if "InterestsAndValues" in target_fields and "InterestsAndValues" in item:
            for field_name, field_list in item["InterestsAndValues"].items():
                if isinstance(field_list, list):
                    for interest_value in field_list:
                        if keyword in interest_value:
                            total_count += 1

    return total_count


def collect_keyword_items(data: List[Dict], keyword: str, target_fields: List[str] = None) -> List[Dict]:
    """收集所有包含关键词的项目信息，用于随机选择删除"""
    if target_fields is None:
        target_fields = SUPPORTED_FIELDS

    items_to_delete = []

    for item_idx, item in enumerate(data):
        # Personality
        if "Personality" in target_fields and "Personality" in item:
            for personality_idx, personality in enumerate(item["Personality"]):
                if keyword in personality:
                    items_to_delete.append({
                        'type': 'Personality',
                        'item_idx': item_idx,
                        'element_idx': personality_idx,
                        'content': personality
                    })

        # Emotional Experience Words
        if "Emotional Experience Words" in target_fields and "Emotional Experience Words" in item:
            for emotion_idx, emotion in enumerate(item["Emotional Experience Words"]):
                if keyword in emotion:
                    items_to_delete.append({
                        'type': 'Emotional Experience Words',
                        'item_idx': item_idx,
                        'element_idx': emotion_idx,
                        'content': emotion
                    })

        # StrengthsAndResources
        if "StrengthsAndResources" in target_fields and "StrengthsAndResources" in item:
            for strength_idx, strength in enumerate(item["StrengthsAndResources"]):
                if keyword in strength:
                    items_to_delete.append({
                        'type': 'StrengthsAndResources',
                        'item_idx': item_idx,
                        'element_idx': strength_idx,
                        'content': strength
                    })

        # SocialSupportSystem
        if "SocialSupportSystem" in target_fields and "SocialSupportSystem" in item:
            for key, value in item["SocialSupportSystem"].items():
                if keyword in key or keyword in value:
                    items_to_delete.append({
                        'type': 'SocialSupportSystem',
                        'item_idx': item_idx,
                        'key': key,
                        'content': f"{key}: {value}"
                    })

        # FormativeExperiences
        if "FormativeExperiences" in target_fields and "FormativeExperiences" in item:
            for exp_idx, experience in enumerate(item["FormativeExperiences"]):
                experience_str = json.dumps(experience, ensure_ascii=False)
                if keyword in experience_str:
                    items_to_delete.append({
                        'type': 'FormativeExperiences',
                        'item_idx': item_idx,
                        'element_idx': exp_idx,
                        'content': str(experience)
                    })

        # InterestsAndValues
        if "InterestsAndValues" in target_fields and "InterestsAndValues" in item:
            for field_name, field_list in item["InterestsAndValues"].items():
                if isinstance(field_list, list):
                    for interest_idx, interest_value in enumerate(field_list):
                        if keyword in interest_value:
                            items_to_delete.append({
                                'type': 'InterestsAndValues',
                                'item_idx': item_idx,
                                'field_name': field_name,
                                'element_idx': interest_idx,
                                'content': interest_value
                            })

    return items_to_delete


def delete_selected_items(data: List[Dict], items_to_delete: List[Dict]) -> None:
    """删除选中的项目"""
    # 按照item_idx和element_idx倒序排序，避免删除时索引变化的问题
    items_to_delete.sort(key=lambda x: (x['item_idx'], x.get('element_idx', -1)), reverse=True)

    for delete_item in items_to_delete:
        item_idx = delete_item['item_idx']
        item = data[item_idx]

        if delete_item['type'] == 'Personality':
            element_idx = delete_item['element_idx']
            if element_idx < len(item['Personality']):
                del item['Personality'][element_idx]

        elif delete_item['type'] == 'Emotional Experience Words':
            element_idx = delete_item['element_idx']
            if element_idx < len(item['Emotional Experience Words']):
                del item['Emotional Experience Words'][element_idx]

        elif delete_item['type'] == 'StrengthsAndResources':
            element_idx = delete_item['element_idx']
            if element_idx < len(item['StrengthsAndResources']):
                del item['StrengthsAndResources'][element_idx]

        elif delete_item['type'] == 'SocialSupportSystem':
            key = delete_item['key']
            if key in item['SocialSupportSystem']:
                del item['SocialSupportSystem'][key]

        elif delete_item['type'] == 'FormativeExperiences':
            element_idx = delete_item['element_idx']
            if element_idx < len(item['FormativeExperiences']):
                del item['FormativeExperiences'][element_idx]

        elif delete_item['type'] == 'InterestsAndValues':
            field_name = delete_item['field_name']
            element_idx = delete_item['element_idx']
            if (field_name in item['InterestsAndValues'] and
                isinstance(item['InterestsAndValues'][field_name], list) and
                element_idx < len(item['InterestsAndValues'][field_name])):
                del item['InterestsAndValues'][field_name][element_idx]


def randomly_delete_keywords(json_file: str, keyword: str, output_file: str = "", target_fields: List[str] = None) -> None:
    """主函数：随机删除包含关键词的字段内容"""
    # 读取JSON文件
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {json_file}")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"错误：{json_file} 不是有效的JSON文件")
        sys.exit(1)

    # 如果指定了字段，显示字段信息
    if target_fields:
        print(f"搜索范围限定在字段: {', '.join(target_fields)}")
    else:
        print(f"搜索范围: 所有支持的字段 ({', '.join(SUPPORTED_FIELDS)})")

    # 统计关键词出现次数
    total_occurrences = count_keyword_occurrences(data, keyword, target_fields)
    print(f"关键词 '{keyword}' 在目标字段中共出现 {total_occurrences} 次")

    if total_occurrences == 0:
        print("没有找到包含该关键词的内容，无需删除")
        return

    # 让用户手动输入删除比例
    while True:
        try:
            deletion_ratio_input = input(f"请输入删除比例 (0-1之间的浮点数，例如0.2表示删除20%): ")
            deletion_ratio = float(deletion_ratio_input)

            if not 0 <= deletion_ratio <= 1:
                print("错误：删除比例必须在0到1之间，请重新输入")
                continue
            break
        except ValueError:
            print("错误：请输入有效的数字，请重新输入")
            continue
        except KeyboardInterrupt:
            print("\n操作已取消")
            sys.exit(0)

    # 计算要删除的数量
    num_to_delete = int(total_occurrences * deletion_ratio)
    print(f"根据删除比例 {deletion_ratio}，将删除 {num_to_delete} 个条目")

    if num_to_delete == 0:
        print("删除数量为0，无需删除")
        return

    # 收集所有包含关键词的项目
    items_to_delete = collect_keyword_items(data, keyword, target_fields)

    # 随机选择要删除的项目
    selected_items = random.sample(items_to_delete, min(num_to_delete, len(items_to_delete)))

    # print(f"随机选择了 {len(selected_items)} 个条目进行删除：")
    # for i, item in enumerate(selected_items, 1):
    #     print(f"{i}. [{item['type']}] {item['content'][:100]}...")

    # 执行删除
    delete_selected_items(data, selected_items)

    # 保存修改后的文件
    if not output_file:
        output_file = json_file.replace('.json', '_modified.json')

    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    print(f"修改后的文件已保存为: {output_file}")


def main():
    parser = argparse.ArgumentParser(description='随机删除JSON文件中包含特定中文词汇的字段内容')
    parser.add_argument('json_file', nargs='?', default=JSON_FILE, help=f'输入的JSON文件路径 (默认: {JSON_FILE})')
    parser.add_argument('keyword', nargs='?', default=KEYWORD, help=f'要搜索的中文词汇 (默认: {KEYWORD})')
    parser.add_argument('--output', '-o', default=OUTPUT_FILE, help='输出文件路径 (默认: 自动生成)')
    parser.add_argument('--fields', '-f', nargs='+', choices=SUPPORTED_FIELDS,
                       help=f'指定搜索的字段范围 (可选: {", ".join(SUPPORTED_FIELDS)})')

    args = parser.parse_args()

    # 显示使用的参数
    print(f"使用参数:")
    print(f"  JSON文件: {args.json_file}")
    print(f"  关键词: {args.keyword}")
    print(f"  输出文件: {args.output if args.output else '自动生成'}")
    if args.fields:
        print(f"  指定字段: {', '.join(args.fields)}")
    print()

    randomly_delete_keywords(args.json_file, args.keyword, args.output, args.fields)


if __name__ == "__main__":
    main()