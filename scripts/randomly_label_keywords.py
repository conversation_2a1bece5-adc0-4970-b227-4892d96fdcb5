#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机标记关键词脚本
用于分析JSON数据中各字段的值分布，并标记概率过高的条目
"""

import json
import random
import argparse
from collections import defaultdict, Counter
from typing import Dict, List, Any, Set, Tuple
import copy


class KeywordAnalyzer:
    """关键词分析器"""

    def __init__(self, target_fields: List[str] = None):
        """
        初始化分析器

        Args:
            target_fields: 要分析的字段列表，如果为None则使用默认字段
        """
        self.default_fields = [
            "Gender", "Age", "Occupation", "Topic", "Subtopic",
            "Personality", "Emotional Experience Words", "Core Drive", "Reaction Pattern"
        ]
        self.target_fields = target_fields or self.default_fields
        self.field_stats = {}
        self.data = []

    def load_data(self, file_path: str) -> None:
        """加载JSON数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 条数据")
        except Exception as e:
            print(f"加载数据失败: {e}")
            raise

    def analyze_field_distribution(self) -> Dict[str, Dict]:
        """分析各字段的值分布"""
        self.field_stats = {}

        for field in self.target_fields:
            field_counter = Counter()
            total_count = 0

            for item in self.data:
                if field not in item:
                    continue

                value = item[field]

                # 处理不同类型的值
                if isinstance(value, list):
                    # 对于列表类型，统计每个元素
                    for v in value:
                        field_counter[str(v)] += 1
                        total_count += 1
                elif isinstance(value, (str, int, float)):
                    # 对于字符串和数字类型
                    field_counter[str(value)] += 1
                    total_count += 1
                else:
                    # 其他类型转为字符串
                    field_counter[str(value)] += 1
                    total_count += 1

            # 计算概率
            probabilities = {}
            for val, count in field_counter.items():
                probabilities[val] = count / total_count if total_count > 0 else 0

            self.field_stats[field] = {
                'counter': field_counter,
                'total_count': total_count,
                'probabilities': probabilities,
                'unique_values': len(field_counter)
            }

        return self.field_stats

    def print_statistics(self) -> None:
        """打印统计信息"""
        print("\n" + "="*80)
        print("字段分布统计")
        print("="*80)

        for field, stats in self.field_stats.items():
            print(f"\n【{field}】")
            print(f"  总计数: {stats['total_count']}")
            print(f"  唯一值数量: {stats['unique_values']}")
            print(f"  平均概率: {1/stats['unique_values']:.2%}")

            # 显示概率最高的前5个值
            sorted_probs = sorted(stats['probabilities'].items(),
                                key=lambda x: x[1], reverse=True)
            print("  概率最高的值:")
            for val, prob in sorted_probs[:5]:
                print(f"    {val}: {prob:.2%} ({stats['counter'][val]}次)")

    def calculate_items_to_label(self, threshold_multiplier: float = 1.5) -> Dict[str, Dict[str, int]]:
        """
        计算需要标记的条目数量，使删除后分布趋向平均

        Args:
            threshold_multiplier: 阈值倍数，超过平均概率的多少倍算作过高

        Returns:
            字段名 -> {值: 需要标记的数量} 的字典
        """
        items_to_label = {}

        for field, stats in self.field_stats.items():
            if stats['unique_values'] == 0:
                continue

            avg_prob = 1 / stats['unique_values']
            threshold = avg_prob * threshold_multiplier

            field_items = {}
            has_high_prob = False

            print(f"\n【{field}】分析:")
            print(f"  平均概率: {avg_prob:.2%}")
            print(f"  阈值: {threshold:.2%}")

            for val, prob in stats['probabilities'].items():
                count = stats['counter'][val]
                if prob > threshold:
                    # 计算需要删除的数量，仅删除超过阈值的部分
                    threshold_count = int(stats['total_count'] * threshold)
                    excess_count = count - threshold_count

                    if excess_count > 0:
                        field_items[val] = excess_count
                        has_high_prob = True
                        print(f"  {val}: {prob:.2%} ({count}次) -> 需标记 {excess_count} 条")

            if has_high_prob:
                items_to_label[field] = field_items
            else:
                print(f"  无需标记的值")

        return items_to_label

    def smart_label_items(self, items_to_label: Dict[str, Dict[str, int]],
                         selected_fields: List[str] = None) -> List[Dict]:
        """
        智能标记数据项，随机选择恰好数量的条目使分布趋向平均

        Args:
            items_to_label: 各字段中需要标记的值和数量
            selected_fields: 选择要标记的字段，如果为None则使用所有字段

        Returns:
            添加了标签的数据列表
        """
        if selected_fields is None:
            selected_fields = self.target_fields

        labeled_data = copy.deepcopy(self.data)
        label_stats = {"total": len(labeled_data), "labeled": 0}

        # 为每个字段的每个高频值收集对应的条目索引
        field_value_indices = {}
        for field in selected_fields:
            if field not in items_to_label:
                continue
            field_value_indices[field] = {}

            for i, item in enumerate(labeled_data):
                if field not in item:
                    continue

                value = item[field]

                # 处理不同类型的值
                if isinstance(value, list):
                    for v in value:
                        val_str = str(v)
                        if val_str in items_to_label[field]:
                            if val_str not in field_value_indices[field]:
                                field_value_indices[field][val_str] = []
                            field_value_indices[field][val_str].append(i)
                elif isinstance(value, (str, int, float)):
                    val_str = str(value)
                    if val_str in items_to_label[field]:
                        if val_str not in field_value_indices[field]:
                            field_value_indices[field][val_str] = []
                        field_value_indices[field][val_str].append(i)

        # 随机选择需要标记的条目
        indices_to_label = set()
        labeling_reasons = {}  # 记录每个索引被标记的原因

        for field, value_counts in items_to_label.items():
            if field not in field_value_indices:
                continue

            print(f"\n处理字段 {field}:")
            for val, needed_count in value_counts.items():
                if val not in field_value_indices[field]:
                    continue

                available_indices = field_value_indices[field][val]
                actual_count = min(needed_count, len(available_indices))

                # 随机选择需要标记的条目
                selected_indices = random.sample(available_indices, actual_count)
                indices_to_label.update(selected_indices)

                # 记录标记原因
                for idx in selected_indices:
                    if idx not in labeling_reasons:
                        labeling_reasons[idx] = []
                    labeling_reasons[idx].append(field)

                print(f"  {val}: 需要标记 {needed_count} 条，实际标记 {actual_count} 条")

        # 应用标记
        for i, item in enumerate(labeled_data):
            should_label = i in indices_to_label
            problem_fields = labeling_reasons.get(i, [])

            # 更新或添加标签字段
            if "Label" in item:
                item["Label"] = item["Label"] or should_label
            else:
                item["Label"] = should_label

            # 更新或添加Label_keys字段
            if "Label_keys" in item:
                existing_keys = set(item["Label_keys"])
                existing_keys.update(problem_fields)
                item["Label_keys"] = list(existing_keys)
            else:
                item["Label_keys"] = problem_fields

            if should_label:
                label_stats["labeled"] += 1

        print(f"\n标记统计:")
        print(f"  总条目数: {label_stats['total']}")
        print(f"  被标记条目数: {label_stats['labeled']}")
        print(f"  标记比例: {label_stats['labeled']/label_stats['total']:.2%}")

        return labeled_data

    def save_labeled_data(self, labeled_data: List[Dict], output_path: str) -> None:
        """保存标记后的数据"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(labeled_data, f, ensure_ascii=False, indent=2)
            print(f"标记后的数据已保存到: {output_path}")
        except Exception as e:
            print(f"保存数据失败: {e}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析JSON数据中各字段的值分布并标记概率过高的条目')
    parser.add_argument('input_file', help='输入JSON文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径（如果不指定则不保存）')
    parser.add_argument('-f', '--fields', nargs='+',
                       help='要分析的字段列表（默认为预设字段）')
    parser.add_argument('-t', '--threshold', type=float, default=2.0,
                       help='概率阈值倍数（默认1.5倍平均概率）')
    parser.add_argument('--label', action='store_true',
                       help='启用标记功能')
    parser.add_argument('--label-fields', nargs='+',
                       help='要标记的字段列表（默认为所有分析字段）')

    parser.add_argument('--stats-only', action='store_true',
                       help='仅显示统计信息，不进行标记')

    args = parser.parse_args()

    # 创建分析器
    analyzer = KeywordAnalyzer(target_fields=args.fields)

    # 加载数据
    print(f"正在加载数据: {args.input_file}")
    analyzer.load_data(args.input_file)

    # 分析字段分布
    print("正在分析字段分布...")
    analyzer.analyze_field_distribution()

    # 显示统计信息
    analyzer.print_statistics()

    if args.stats_only:
        print("\n仅显示统计信息模式，程序结束。")
        return

    labeled_data = None

    if args.label:
        # 基于概率的智能标记模式
        print(f"\n计算需要标记的条目（阈值倍数: {args.threshold}）...")
        items_to_label = analyzer.calculate_items_to_label(args.threshold)

        if any(items_to_label.values()):
            print("\n开始智能标记条目...")
            labeled_data = analyzer.smart_label_items(
                items_to_label=items_to_label,
                selected_fields=args.label_fields
            )
        else:
            print("没有发现需要标记的值，数据分布已较为均匀。")

    # 保存结果
    if labeled_data and args.output:
        analyzer.save_labeled_data(labeled_data, args.output)
    elif labeled_data:
        print("\n注意: 未指定输出文件，标记后的数据未保存。")

    print("\n程序执行完成。")


if __name__ == "__main__":
    main()