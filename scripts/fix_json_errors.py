#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON格式错误修复工具
专门修复expand_0726_4000.jsonl中发现的具体格式错误
"""

import json
import sys
import re
from pathlib import Path


class JSONErrorFixer:
    """JSON错误修复器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.content = ""
        self.fixes_applied = []
        
    def fix_file(self, output_path: str = None):
        """修复文件中的JSON错误"""
        print(f"🔧 开始修复文件: {self.file_path}")
        
        # 读取文件
        self._load_file()
        
        # 应用修复
        self._apply_fixes()
        
        # 验证修复结果
        self._validate_fixes()
        
        # 保存修复后的文件
        if output_path is None:
            output_path = self.file_path.replace('.jsonl', '_fixed.jsonl')
        
        self._save_file(output_path)
        
        print(f"✅ 修复完成，保存到: {output_path}")
        return output_path
    
    def _load_file(self):
        """加载文件内容"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.content = f.read()
            print(f"📁 文件加载成功，总字符数: {len(self.content):,}")
        except Exception as e:
            print(f"❌ 文件加载失败: {e}")
            sys.exit(1)
    
    def _apply_fixes(self):
        """应用所有修复"""
        print(f"🛠️ 开始应用修复...")
        
        original_content = self.content
        
        # 修复1: 处理FormativeExperiences中的多余字符
        self._fix_formative_experiences_error()
        
        # 修复2: 处理Values中的错误字符
        self._fix_values_error()
        
        # 修复3: 处理未转义的引号
        self._fix_unescaped_quotes()
        
        # 修复4: 处理字符串中的换行符
        self._fix_newlines_in_strings()
        
        if len(self.fixes_applied) > 0:
            print(f"✅ 应用了 {len(self.fixes_applied)} 个修复:")
            for fix in self.fixes_applied:
                print(f"  - {fix}")
        else:
            print("ℹ️ 未发现需要修复的错误")
    
    def _fix_formative_experiences_error(self):
        """修复FormativeExperiences中的格式错误"""
        # 查找并修复类似 "}], " 后面跟着不正确结构的问题
        pattern = r'(\}\s*\]\s*,\s*"\s*\}\s*\]\s*,)'
        matches = list(re.finditer(pattern, self.content))
        
        if matches:
            for match in reversed(matches):  # 从后往前替换，避免位置偏移
                start, end = match.span()
                # 替换为正确的结构
                self.content = self.content[:start] + '}],' + self.content[end:]
                self.fixes_applied.append(f"修复FormativeExperiences格式错误 (位置 {start}-{end})")
    
    def _fix_values_error(self):
        """修复Values中的错误字符"""
        # 查找并修复 】}}" 这样的错误字符
        error_patterns = [
            (r'】\}\}"', '")'),  # 】}}" -> ")
            (r'】\}"', '")'),   # 】}" -> ")
            (r'】', ')'),       # 】 -> )
        ]
        
        for pattern, replacement in error_patterns:
            matches = list(re.finditer(pattern, self.content))
            if matches:
                for match in reversed(matches):
                    start, end = match.span()
                    self.content = self.content[:start] + replacement + self.content[end:]
                    self.fixes_applied.append(f"修复错误字符 '{match.group()}' -> '{replacement}' (位置 {start}-{end})")
    
    def _fix_unescaped_quotes(self):
        """修复字符串中未转义的引号"""
        # 这个比较复杂，需要小心处理，只处理明显的错误情况
        
        # 查找字符串值中的未转义引号（在JSON字符串内部的引号）
        # 模式：查找 "key": "value with " unescaped quote"
        pattern = r'("(?:[^"\\]|\\.)*")\s*:\s*("(?:[^"\\]|\\.)*"[^"]*"(?:[^"\\]|\\.)*")'
        
        def fix_quotes_in_match(match):
            key = match.group(1)
            value = match.group(2)
            
            # 如果value中包含未转义的引号，尝试修复
            if value.count('"') > 2:  # 开头和结尾的引号 + 内部的引号
                # 简单修复：将内部的引号转义
                inner_content = value[1:-1]  # 去掉开头和结尾的引号
                fixed_inner = inner_content.replace('"', '\\"')
                fixed_value = f'"{fixed_inner}"'
                return f'{key}: {fixed_value}'
            
            return match.group(0)
        
        original_content = self.content
        self.content = re.sub(pattern, fix_quotes_in_match, self.content)
        
        if self.content != original_content:
            self.fixes_applied.append("修复字符串中的未转义引号")
    
    def _fix_newlines_in_strings(self):
        """修复字符串中的换行符"""
        # 暂时禁用这个修复，因为它会过度修复
        pass
    
    def _validate_fixes(self):
        """验证修复结果"""
        print(f"🔍 验证修复结果...")
        
        # 使用JSONDecoder验证
        decoder = json.JSONDecoder()
        idx = 0
        valid_objects = 0
        errors = []
        
        while idx < len(self.content) and len(errors) < 5:  # 最多显示5个错误
            content_from_idx = self.content[idx:].lstrip()
            if not content_from_idx:
                break
            
            try:
                obj, end_idx = decoder.raw_decode(content_from_idx)
                valid_objects += 1
                idx += len(self.content[idx:]) - len(content_from_idx) + end_idx
                
                if valid_objects % 1000 == 0:
                    print(f"  验证了 {valid_objects} 个对象...")
                    
            except json.JSONDecodeError as e:
                error_pos = idx + len(self.content[idx:]) - len(content_from_idx)
                errors.append({
                    'position': error_pos,
                    'error': str(e),
                    'context': content_from_idx[:200]
                })
                
                # 尝试跳过错误继续验证
                next_brace = self.content.find('{', idx + 1)
                if next_brace == -1:
                    break
                idx = next_brace
        
        print(f"✅ 验证完成: {valid_objects} 个有效JSON对象")
        
        if errors:
            print(f"❌ 仍有 {len(errors)} 个解析错误:")
            for i, error in enumerate(errors, 1):
                print(f"  {i}. 位置 {error['position']}: {error['error']}")
        else:
            print("🎉 所有JSON对象都能正确解析!")
    
    def _save_file(self, output_path: str):
        """保存修复后的文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(self.content)
            print(f"💾 文件保存成功: {output_path}")
        except Exception as e:
            print(f"❌ 文件保存失败: {e}")
            sys.exit(1)


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python fix_json_errors.py <input_file> [output_file]")
        print("示例: python fix_json_errors.py expand_0726_4000.jsonl")
        print("示例: python fix_json_errors.py expand_0726_4000.jsonl expand_0726_4000_fixed.jsonl")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    fixer = JSONErrorFixer(input_file)
    fixed_file = fixer.fix_file(output_file)
    
    print(f"\n🎯 修复完成! 请使用以下命令验证:")
    print(f"python scripts/invalid_detect.py {fixed_file}")


if __name__ == "__main__":
    main()
